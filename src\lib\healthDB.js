import Dexie from 'dexie';

/**
 * 健康数据库类
 * 专门用于管理健康数据(体重等)的存储
 */
class HealthDB extends Dexie {
  constructor() {
    super('HealthDB');
    
    // 定义数据库版本和表结构
    this.version(1).stores({
      healthRecords: '++id, date, weight, notes, createdAt, updatedAt'
    });

    this.healthRecords = this.table('healthRecords');
  }
}

// 创建数据库实例
const healthDB = new HealthDB();

/**
 * 健康数据库操作方法
 */
export const healthDBOperations = {
  /**
   * 添加健康记录
   * @param {Object} record - 健康记录对象
   * @param {string} record.date - 日期
   * @param {number} record.weight - 体重
   * @param {string} [record.notes] - 备注
   */
  async addHealthRecord(record) {
    try {
      const cleanRecord = {
        date: record.date,
        weight: record.weight,
        notes: record.notes || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await healthDB.healthRecords.add(cleanRecord);
      console.log('健康记录已保存:', cleanRecord);
      return result;
    } catch (error) {
      console.error('添加健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 更新健康记录
   * @param {number} id - 记录ID
   * @param {Object} updates - 更新内容
   */
  async updateHealthRecord(id, updates) {
    try {
      const cleanUpdates = {
        ...updates,
        updatedAt: new Date()
      };
      
      const result = await healthDB.healthRecords.update(id, cleanUpdates);
      console.log('健康记录已更新:', { id, updates: cleanUpdates });
      return result;
    } catch (error) {
      console.error('更新健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 删除健康记录
   * @param {number} id - 记录ID
   */
  async deleteHealthRecord(id) {
    try {
      const result = await healthDB.healthRecords.delete(id);
      console.log('健康记录已删除:', id);
      return result;
    } catch (error) {
      console.error('删除健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有健康记录
   * @returns {Promise<Array>} 健康记录数组
   */
  async getAllHealthRecords() {
    try {
      const records = await healthDB.healthRecords
        .orderBy('date')
        .reverse()
        .toArray();
      
      console.log('获取所有健康记录:', records);
      return records;
    } catch (error) {
      console.error('获取健康记录失败:', error);
      throw error;
    }
  },

  /**
   * 根据日期获取健康记录
   * @param {string} date - 日期
   * @returns {Promise<Object|null>} 健康记录或null
   */
  async getHealthRecordByDate(date) {
    try {
      const record = await healthDB.healthRecords
        .where('date')
        .equals(date)
        .first();
      
      return record;
    } catch (error) {
      console.error('根据日期获取健康记录失败:', error);
      throw error;
    }
  }
};

// 默认导出数据库实例
export default healthDB;
