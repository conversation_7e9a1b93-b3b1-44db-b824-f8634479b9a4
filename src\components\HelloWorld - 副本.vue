<template>
  <div class="desktop-container">
    <!-- Dynamic background with animated elements -->
    <div class="background">
      <div class="particles"></div>
    </div>

    <!-- Main content -->
    <div class="desktop-content">
      <!-- Top section with time and date -->
      <div class="top-section">
        <div class="time-display">
          <div class="time">{{ currentTime }}</div>
          <div class="date">{{ currentDate }}</div>
        </div>

        <!-- Enhanced search box -->
        <div class="search-container">
          <div class="search-box">
            <Search class="search-icon" />
            <input
              type="text"
              placeholder="请输入搜索内容"
              class="search-input"
              v-model="searchQuery"
              @keyup.enter="performSearch"
            />
            <select v-model="selectedEngine" class="search-engine-select">
              <option value="https://www.baidu.com/s?wd=">百度</option>
              <option value="https://www.bing.com/search?q=">必应</option>
              <option value="https://www.sogou.com/web?query=">搜狗</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Dock with glass morphism effect -->
      <div class="dock-wrapper">
        <div class="dock">
          <div
            v-for="dockApp in dockApps"
            :key="dockApp.name"
            @click="openApp(dockApp)"
            class="dock-item"
            :class="{ pulse: dockApp.highlight }"
          >
            <div class="dock-icon" :style="{ background: dockApp.color }">
              <component :is="dockApp.icon" class="dock-icon-svg" />
            </div>
            <div class="dock-tooltip">{{ dockApp.name }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户端组件 -->
    <!-- 密码库 -->
    <PasswordVault :show="showPasswordVault" @close="closePasswordVault" />
    <!-- 代码组件中心 -->
    <CodeCenterModal :show="showCodeCenter" @close="closeCodeCenter" />
    <!-- 备忘录 -->
    <MemoModal :show="showMemo" @close="closeMemo" />
    <!-- 健康体重中心 -->
    <HealthModal :show="showHealth" @close="closeHealth" />
    <!-- 美食MALL -->
    <FoodMall :show="showFoodMall" @close="closeFoodMall" />
    <!-- ToDoList -->
    <ToDoList :show="showToDoList" @close="closeToDoList" />

    <!-- 管理端组件 -->
    <AdminMain
      :show="showAdminMain"
      @close="closeAdminMain"
      @openUserManagement="openAFoodMall"
      @openAMemo="openAMemo"
    />
    />
    <AFoodMall :show="showAFoodMall" @close="closeAFoodMall" />
    <AMemo :show="showAMemo" @close="closeAMemo" />

    <!-- 成功提示 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
    <!-- 便签组件列表 -->
    <div v-for="(note, index) in notes" :key="note.id" class="note-wrapper">
      <div
        class="sticky-note"
        :style="{
          left: `${note.x}px`,
          top: `${note.y}px`,
          zIndex: note.isDragging ? 100 : index + 10,
          backgroundColor: note.color,
          borderColor: getBorderColor(note.color),
        }"
        @mousedown="startDrag(note, $event)"
        @click="bringToFront(note)"
        :class="{
          dragging: note.isDragging,
          'new-note': note.isNew,
        }"
      >
        <!-- 便签头部 - 包含标题、颜色选择和删除按钮 -->
        <div class="note-header">
          <span class="note-title">{{ note.name }}</span>

          <!-- 颜色选择器 -->
          <div class="color-picker" @click.stop>
            <button class="color-btn" :style="{ backgroundColor: note.color }">
              <!-- <el-icon><Operation /></el-icon> -->
              <el-icon><DataAnalysis /></el-icon>
            </button>
            <div class="color-options">
              <div
                v-for="color in colorOptions"
                :key="color.value"
                class="color-option"
                :style="{ backgroundColor: color.value }"
                @click="changeNoteColor(note.id, color.value)"
                :title="color.name"
              ></div>
            </div>
          </div>

          <button
            class="delete-btn"
            @click.stop="deleteNote(note.id)"
            aria-label="删除便签"
          >
          <el-icon><Delete /></el-icon>
            <!-- <i class="fas fa-times"></i> -->
          </button>
        </div>

        <!-- 便签内容区域 -->
        <textarea
          class="note-content"
          v-model="note.content"
          placeholder="在这里输入你的笔记内容..."
          @click.stop
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from "vue";
import {
  Search,
  Play,
  Archive,
  UtensilsCrossed,
  MessageCircle,
  Zap,
  Star,
  Cloud,
  Compass,
  Coffee,
  Code,
  Cpu,
  StickyNote,
  Heart,
  Shield,
  Smile,
  User,
  ListTodo,
  ScrollText,
  // code-xml
} from "lucide-vue-next";

import { ElMessage, ElMessageBox } from "element-plus";
// 按需引入你需要的图标组件
import { Operation,Delete, DataAnalysis } from '@element-plus/icons-vue'

// 各个组件引入
import MemoModal from "./MemoModal.vue";
import CodeCenterModal from "./CodeCenter.vue";
import PasswordVault from "./PasswordVault.vue";
import HealthModal from "./HealthModal.vue";
import FoodMall from "./FoodMall.vue";
import ToDoList from "./ToDoList.vue";
import AFoodMall from "../admin/AFoodMall.vue";
import AMemo from "../admin/AMemo.vue";

// admin管理端
import AdminMain from "../admin/Main.vue";

// Reactive data
const currentTime = ref("");
const currentDate = ref("");
const selectedEngine = ref("https://www.baidu.com/s?wd=");
const searchQuery = ref("");
const showMemo = ref(false);
const showCodeCenter = ref(false);
const showHealth = ref(false);
const showPasswordVault = ref(false);
const showFoodMall = ref(false);
const showToDoList = ref(false);

const showAdminMain = ref(false);
const showAFoodMall = ref(false);
const showAMemo = ref(false);

// Dock applications with enhanced styling and icons
const dockApps = ref([
  {
    name: "思盒OA",
    icon: Coffee,
    color: "linear-gradient(135deg, #00d4aa, #00a884)",
    path: "http://192.168.28.8/hsoftoa/#/login?redirect=%2Fdashboard",
    highlight: true,
  },
  {
    name: "ToDoList",
    icon: ListTodo,
    color: "linear-gradient(135deg, #ff7e5f, #feb47b)",
    action: "openToDoList",
    highlight: false,
  },
  {
    name: "CodeCenter",
    icon: Code,
    color: "linear-gradient(135deg, #bdc3c7, #2c3e50)",
    action: "openCodeCenter",
    highlight: false,
  },
  {
    name: "备忘录",
    icon: StickyNote,
    color: "linear-gradient(135deg, #ff9a9e, #fad0c4)",
    action: "openMemo",
    highlight: false,
  },
  {
    name: "密码库",
    icon: Shield,
    color: "linear-gradient(135deg, #667eea, #764ba2)",
    action: "openPasswordVault",
    highlight: false,
  },
  {
    name: "运动健康",
    icon: Heart,
    color: "linear-gradient(135deg, #ff6b6b, #ee5a52)",
    action: "openHealth",
    highlight: false,
  },
  {
    name: "美食MALL",
    icon: UtensilsCrossed,
    color: "linear-gradient(135deg, #55a3ff, #2e86de)",
    action: "openFoodMall",
    highlight: false,
  },
  {
    name: "便签",
    icon: ScrollText,
    color: "linear-gradient(135deg, #000000, #434343, #00ff9d)",
    action: "openNote",
    highlight: false,
  },
  {
    name: "管理端",
    icon: User,
    color: "linear-gradient(135deg, #00b894, #00cec9)",
    action: "openAdminMain",
    highlight: false,
  },
]);

// Search function
function performSearch() {
  if (searchQuery.value.trim()) {
    window.open(selectedEngine.value + encodeURIComponent(searchQuery.value), "_blank");
    searchQuery.value = "";
  }
}

// Update time function
const updateTime = () => {
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  currentTime.value = `${hours}:${minutes}`;

  const options = {
    month: "long",
    day: "numeric",
    weekday: "long",
  };
  currentDate.value = now.toLocaleDateString("zh-CN", options) + " 五月初三";
};

// Open application function
const openApp = (app) => {
  //
  if (app.action === "openCodeCenter") {
    openCodeCenter();
  } else if (app.action === "openMemo") {
    openMemo();
  } else if (app.action === "openHealth") {
    openHealth();
  } else if (app.action === "openPasswordVault") {
    openPasswordVault();
  } else if (app.action === "openFoodMall") {
    openFoodMall();
  } else if (app.action === "openParticleImage") {
    openParticleImage();
  } else if (app.action === "openAdminMain") {
    openAdminMain();
  } else if (app.action === "openAFoodMall") {
    openAFoodMall();
  } else if (app.action === "openAMemo") {
    openAMemo();
  } else if (app.action === "openToDoList") {
    openToDoList();
  } else if (app.action === "openNote") {
    openNote();
  } else if (app.path) {
    window.open(app.path, "_blank");
  }
};
// 添加新便签
const openNote = () => {
  // 增加计数并生成名称
  noteCounter.value++;
  const noteName = `便签${noteCounter.value}`;

  // 默认位置：屏幕中心偏上，每次创建位置略有偏移
  const offset = (noteCounter.value - 1) * 20;
  const defaultX = (window.innerWidth - 300) / 2 + (offset % 100);
  const defaultY = (window.innerHeight - 300) / 2 - 100 + Math.floor(offset / 100) * 20;

  // 创建新便签（JSON结构）
  const newNote = {
    id: generateId(),
    name: noteName,
    content: "",
    color: colorOptions[0].value, // 默认第一个颜色
    x: defaultX,
    y: defaultY,
    isDragging: false,
    isNew: true, // 用于新创建动画
  };

  notes.value.push(newNote);

  // 显示成功提示
  showNotification(`"${noteName}" 创建成功`);

  // 按钮动画
  showAddAnimation.value = true;
  setTimeout(() => {
    showAddAnimation.value = false;
  }, 500);

  // 3秒后移除新便签标记（结束动画）
  setTimeout(() => {
    const noteIndex = notes.value.findIndex((n) => n.id === newNote.id);
    if (noteIndex !== -1) {
      notes.value[noteIndex].isNew = false;
    }
  }, 1000);
};

// Modal functions
const openPasswordVault = () => {
  showPasswordVault.value = true;
};

const closePasswordVault = () => {
  showPasswordVault.value = false;
};

const openMemo = () => {
  showMemo.value = true;
};

const closeMemo = () => {
  showMemo.value = false;
};

const openCodeCenter = () => {
  showCodeCenter.value = true;
};

const closeCodeCenter = () => {
  showCodeCenter.value = false;
};

const openHealth = () => {
  showHealth.value = true;
};

const closeHealth = () => {
  showHealth.value = false;
};
const openFoodMall = () => {
  showFoodMall.value = true;
};
const closeFoodMall = () => {
  showFoodMall.value = false;
};
const openToDoList = () => {
  showToDoList.value = true;
};
const closeToDoList = () => {
  showToDoList.value = false;
};
const openAFoodMall = () => {
  showAFoodMall.value = true;
};
const closeAFoodMall = () => {
  showAFoodMall.value = false;
};
const openAMemo = () => {
  showAMemo.value = true;
};
const closeAMemo = () => {
  showAMemo.value = false;
};
const openAdminMain = () => {
  // showParticleImage.value = true;
  ElMessageBox.prompt("请输入唯一KEY", "KEY", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    // inputPattern:
    //   /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
    // inputErrorMessage: 'Invalid Email',
  })
    .then(({ value }) => {
      // showAdminMain.value = true;
      value = "zhangzuying";
      console.log(value, "306---");
      if (value == "zhangzuying") {
        showAdminMain.value = true;
      } else {
        ElMessage({
          type: "info",
          message: "口令错误，无法进入管理端！",
        });
      }
      // ElMessage({
      //   type: 'success',
      //   message: `Your email is:${value}`,
      // })
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "Input canceled",
      });
    });
};
const closeAdminMain = () => {
  showAdminMain.value = false;
};

// Set up time interval
let timeInterval;

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  createParticles();
  // 便签逻辑start
  window.addEventListener('mousemove', handleMouseMove);
  window.addEventListener('mouseup', handleMouseUp);
  window.addEventListener('resize', handleResize);
  // 便签逻辑end
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
  // 便签逻辑start
  window.removeEventListener('mousemove', handleMouseMove);
  window.removeEventListener('mouseup', handleMouseUp);
  window.removeEventListener('resize', handleResize);
  // 便签逻辑end
});

// Function to create particle effect
const createParticles = () => {
  const particlesContainer = document.querySelector(".particles");
  if (!particlesContainer) return;

  for (let i = 0; i < 50; i++) {
    const particle = document.createElement("div");
    particle.classList.add("particle");
    particle.style.left = Math.random() * 100 + "vw";
    particle.style.top = Math.random() * 100 + "vh";
    const size = Math.random() * 5 + 2;
    particle.style.width = size + "px";
    particle.style.height = size + "px";
    particle.style.animationDelay = Math.random() * 5 + "s";
    particle.style.animationDuration = Math.random() * 10 + 10 + "s";
    particlesContainer.appendChild(particle);
  }
};


// 便签的逻辑start
// 颜色选项 - 10种不同背景色
const colorOptions = [
  { name: '浅黄色', value: '#fff9c4' },
  { name: '浅粉色', value: '#ffebee' },
  { name: '浅蓝色', value: '#e3f2fd' },
  { name: '浅绿色', value: '#e8f5e9' },
  { name: '浅紫色', value: '#f3e5f5' },
  { name: '浅橙色', value: '#fff3e0' },
  { name: '浅青色', value: '#e0f7fa' },
  { name: '浅红色', value: '#ffe0e0' },
  { name: '浅棕色', value: '#f5f5dc' },
  { name: '浅灰色', value: '#f5f5f5' }
];

// 生成唯一ID的函数
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 便签数据 - JSON结构
const notes = ref([]);
// 当前便签计数，用于命名
const noteCounter = ref(0);
// 当前正在拖拽的便签
const draggingNote = ref(null);
// 拖拽的偏移量
const dragOffset = ref({ x: 0, y: 0 });
// 通知提示
const notification = ref({
  show: false,
  message: '',
  type: 'success'
});
// 添加按钮动画
const showAddAnimation = ref(false);

// 计算边框颜色（基于背景色加深）
const getBorderColor = (color) => {
  // 简单的颜色转换示例，实际项目中可使用更精确的颜色处理库
  const colorMap = {
    '#fff9c4': '#fbc02d',
    '#ffebee': '#ef9a9a',
    '#e3f2fd': '#90caf9',
    '#e8f5e9': '#a5d6a7',
    '#f3e5f5': '#ce93d8',
    '#fff3e0': '#ffcc80',
    '#e0f7fa': '#80deea',
    '#ffe0e0': '#ffcdd2',
    '#f5f5dc': '#d7ccc8',
    '#f5f5f5': '#e0e0e0'
  };
  return colorMap[color] || '#ccc';
};

// 添加新便签
const addNewNote = () => {
  // 增加计数并生成名称
  noteCounter.value++;
  const noteName = `便签${noteCounter.value}`;
  
  // 默认位置：屏幕中心偏上，每次创建位置略有偏移
  const offset = (noteCounter.value - 1) * 20;
  const defaultX = (window.innerWidth - 300) / 2 + (offset % 100);
  const defaultY = (window.innerHeight - 300) / 2 - 100 + Math.floor(offset / 100) * 20;
  
  // 创建新便签（JSON结构）
  const newNote = {
    id: generateId(),
    name: noteName,
    content: '',
    color: colorOptions[0].value, // 默认第一个颜色
    x: defaultX,
    y: defaultY,
    isDragging: false,
    isNew: true // 用于新创建动画
  };
  
  notes.value.push(newNote);
  
  // 显示成功提示
  showNotification(`"${noteName}" 创建成功`);
  
  // 按钮动画
  showAddAnimation.value = true;
  setTimeout(() => {
    showAddAnimation.value = false;
  }, 500);
  
  // 3秒后移除新便签标记（结束动画）
  setTimeout(() => {
    const noteIndex = notes.value.findIndex(n => n.id === newNote.id);
    if (noteIndex !== -1) {
      notes.value[noteIndex].isNew = false;
    }
  }, 1000);
};

// 删除便签
const deleteNote = (id) => {
  const note = notes.value.find(n => n.id === id);
  if (note) {
    // 添加删除动画
    const noteEl = document.querySelector(`[data-note-id="${id}"]`);
    if (noteEl) {
      noteEl.classList.add('delete-animation');
    }
    
    // 延迟删除，等待动画完成
    setTimeout(() => {
      notes.value = notes.value.filter(note => note.id !== id);
      showNotification(`"${note.name}" 已删除`, 'info');
    }, 300);
  }
};

// 更改便签颜色
const changeNoteColor = (id, color) => {
  const note = notes.value.find(n => n.id === id);
  if (note) {
    note.color = color;
    // 添加颜色变化动画
    const noteEl = document.querySelector(`[data-note-id="${id}"]`);
    if (noteEl) {
      noteEl.classList.add('color-change');
      setTimeout(() => {
        noteEl.classList.remove('color-change');
      }, 300);
    }
  }
};

// 开始拖拽
const startDrag = (note, event) => {
  // 只有点击头部才可以拖拽
  if (!event.target.closest('.note-header')) return;
  
  draggingNote.value = note;
  note.isDragging = true;
  
  // 计算鼠标相对于便签左上角的偏移量
  dragOffset.value.x = event.clientX - note.x;
  dragOffset.value.y = event.clientY - note.y;
  
  // 阻止默认行为，避免拖拽时选中文本
  event.preventDefault();
};

// 处理拖拽移动
const handleMouseMove = (event) => {
  if (draggingNote.value) {
    // 计算新位置
    const newX = event.clientX - dragOffset.value.x;
    const newY = event.clientY - dragOffset.value.y;
    
    // 确保便签不会完全超出可视区域
    const maxX = window.innerWidth - 300;
    const maxY = window.innerHeight - 300;
    
    draggingNote.value.x = Math.max(0, Math.min(newX, maxX));
    draggingNote.value.y = Math.max(0, Math.min(newY, maxY));
  }
};

// 结束拖拽
const handleMouseUp = () => {
  if (draggingNote.value) {
    draggingNote.value.isDragging = false;
    draggingNote.value = null;


    // console.log(notes.value, '676---')
  }
};

// 将便签置于顶层
const bringToFront = (note) => {
  // 通过重新排序数组来改变z-index
  const index = notes.value.findIndex(n => n.id === note.id);
  if (index !== -1) {
    const [removed] = notes.value.splice(index, 1);
    notes.value.push(removed);
    
    // 添加前置动画
    const noteEl = document.querySelector(`[data-note-id="${note.id}"]`);
    if (noteEl) {
      noteEl.classList.add('bring-forward');
      setTimeout(() => {
        noteEl.classList.remove('bring-forward');
      }, 300);
    }
  }
};

// 显示通知
const showNotification = (message, type = 'success') => {
  notification.value = {
    show: true,
    message,
    type
  };
  
  // 3秒后自动关闭
  setTimeout(() => {
    notification.value.show = false;
  }, 3000);
};

// 监听窗口大小变化，调整便签位置避免超出屏幕
const handleResize = () => {
  notes.value.forEach(note => {
    const maxX = window.innerWidth - 300;
    const maxY = window.innerHeight - 300;
    
    note.x = Math.max(0, Math.min(note.x, maxX));
    note.y = Math.max(0, Math.min(note.y, maxY));
  });
};
// 监听便签变化，可用于持久化存储
watch(notes, (newNotes) => {
  console.log('便签数据变化:', JSON.stringify(newNotes, null, 2));
  // 实际应用中可以在这里保存到localStorage或服务器
}, { deep: true });
// 便签的逻辑end
</script>

<style scoped>
button:focus {
  outline: none;
}
/* Base container */
.desktop-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  color: white;
}

/* Enhanced background with more vibrant gradients */
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* background: linear-gradient(135deg, #0f0c29, #302b63, #24243e); */
  overflow: hidden;
  background-image: url("../assets/bg8.jpg");
  background-size: cover;
}

/* Particles effect */
.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.particle {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: rise linear infinite;
}

@keyframes rise {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(20px);
    opacity: 0;
  }
}

/* Main content */
.desktop-content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Top section with time and search */
.top-section {
  text-align: center;
  margin-bottom: 30px;
  padding-top: 40px;
}

/* Time display with enhanced typography */
.time-display {
  margin-bottom: 30px;
}

.time {
  font-size: 5rem;
  font-weight: 200;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  margin-bottom: 5px;
  letter-spacing: 2px;
}

.date {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  letter-spacing: 1px;
}

/* Enhanced search container */
.search-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

/* Glass morphism search box */
.search-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 8px 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 600px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.search-box:hover,
.search-box:focus-within {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.search-icon {
  margin-right: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* Transparent input field */
.search-input {
  border: none;
  outline: none;
  flex: 1;
  font-size: 16px;
  padding: 10px 0;
  margin-right: 12px;
  background: transparent;
  color: white;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Stylish select dropdown */
.search-engine-select {
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 8px 16px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  position: relative;
}

.search-engine-select:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.search-engine-select option {
  background: #302b63;
  color: white;
}

/* Dock wrapper for positioning */
.dock-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  padding-bottom: 10px;
}

/* Enhanced dock with glass morphism */
.dock {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  flex-wrap: wrap;
}

.dock:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

/* Enhanced dock items with hover effects */
.dock-item {
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.dock-item:hover {
  transform: translateY(-12px) scale(1.1);
}

/* Dock icons with gradient backgrounds */
.dock-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dock-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  border-radius: 14px;
}

.dock-icon-svg {
  width: 28px;
  height: 28px;
  color: white;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
  z-index: 1;
}

/* Tooltip that appears on hover */
.dock-tooltip {
  position: absolute;
  top: -40px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 6px;
  font-size: 12px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
}

.dock-item:hover .dock-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Pulse animation for highlighted items */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* 便签的样式start */
.app-container {
  min-height: 100vh;
  background-color: #f0f2f5;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.4) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(255,255,255,0.4) 0%, transparent 20%);
  padding: 20px;
  transition: background-color 0.5s ease;
}

.add-note-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: #42b983;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(66, 185, 131, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1000;
}

.add-note-btn:hover {
  transform: scale(1.1);
  background-color: #359e75;
  box-shadow: 0 6px 16px rgba(66, 185, 131, 0.5);
}

.add-note-btn i {
  margin-right: 0;
}

.pulse-animation {
  animation: pulse 0.5s ease;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@media (min-width: 768px) {
  .add-note-btn {
    width: auto;
    height: 60px;
    padding: 0 20px;
    border-radius: 30px;
    font-size: 16px;
  }
  
  .add-note-btn i {
    margin-right: 8px;
  }
}

.notification {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 30px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  animation: slideDown 0.3s ease forwards, fadeOut 0.3s ease 2.7s forwards;
}

.notification.success {
  background-color: #4caf50;
}

.notification.info {
  background-color: #2196f3;
}

@keyframes slideDown {
  from { transform: translate(-50%, -100px); opacity: 0; }
  to { transform: translate(-50%, 0); opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.sticky-note {
  position: fixed;
  width: 300px;
  height: 300px;
  border-radius: 8px;
  border: 1px solid;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: default;
}

.sticky-note.new-note {
  animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes popIn {
  0% { transform: scale(0.8); opacity: 0; }
  70% { transform: scale(1.05); }
  100% { transform: scale(1); opacity: 1; }
}

.sticky-note.color-change {
  animation: colorShift 0.3s ease;
}

@keyframes colorShift {
  0% { transform: rotate(-1deg); }
  50% { transform: rotate(1deg); }
  100% { transform: rotate(0); }
}

.sticky-note.bring-forward {
  animation: liftUp 0.3s ease;
}

@keyframes liftUp {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}

.sticky-note.delete-animation {
  animation: fadeAway 0.3s ease forwards;
}

@keyframes fadeAway {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0; }
}

.sticky-note:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.sticky-note.dragging {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  transform: scale(1.02) rotate(1deg);
  transition: none;
}

.note-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  transition: background-color 0.3s ease;
}

.note-title {
  font-weight: bold;
  color: #333;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.color-picker {
  position: relative;
  margin: 0 8px;
}

.color-btn {
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #555;
  transition: all 0.2s ease;
}

.color-btn:hover {
  transform: rotate(30deg);
  color: #222;
}

.color-options {
  position: absolute;
  top: 30px;
  right: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  padding: 8px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
  width: 160px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.color-picker:hover .color-options {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s ease;
  border: 1px solid #ddd;
}

.color-option:hover {
  transform: scale(1.2);
}

.delete-btn {
  background: none;
  border: none;
  color: #795548;
  cursor: pointer;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: #d32f2f;
  transform: rotate(90deg);
}

.note-content {
  width: 100%;
  height: calc(100% - 40px);
  border: none;
  background-color: transparent;
  padding: 12px;
  resize: none;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  transition: all 0.3s ease;
}

.note-content:focus {
  outline: none;
  background-color: rgba(255, 255, 255, 0.1);
}

.note-content::placeholder {
  color: #888;
  font-style: italic;
  opacity: 0.7;
}
/* 便签的样式end */
</style>
