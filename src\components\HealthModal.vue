<template>
  <!-- 主健康模态框 -->
  <Transition name="modal-fade">
    <div v-if="show" class="health-overlay" @click.self="$emit('close')">
      <Transition name="health-bounce">
        <div v-if="show" class="health-container">
          <!-- 粒子效果 -->
          <div class="health-particles">
            <div v-for="i in 20" :key="i" class="health-particle" :style="getParticleStyle(i)"></div>
          </div>

          <!-- 头部 -->
          <div class="health-header">
            <div class="health-title-container">
              <h2 class="health-title">饮食运动记录表</h2>
              <div class="health-title-glow"></div>
            </div>
            <p class="health-subtitle">原则：低糖、低脂、高蛋白、高纤维</p>
            <button @click="$emit('close')" class="health-close-btn">
              <X class="health-icon" />
            </button>
          </div>

          <!-- 内容区域 -->
          <div class="health-content">
            <!-- 控制栏 -->
            <div class="health-controls">
              <div class="health-search-box">
                <input
                  type="text"
                  placeholder="搜索记录..."
                  v-model="searchQuery"
                  class="health-search-input"
                />
                <div class="health-search-icon">
                  <Search />
                </div>
              </div>
              <div class="health-control-buttons">
                <button class="health-add-button" @click="openAddModal">
                  <Plus />
                  <span>新增记录</span>
                </button>
                <button class="health-filter-button" @click="openWeightStats">
                  <span>体重统计</span>
                  <TrendingDown />
                </button>
              </div>
            </div>

            <!-- 表格 -->
            <div class="health-table-wrapper">
              <table class="health-table">
                <thead>
                  <tr>
                    <th>日期</th>
                    <th>怎么执行的</th>
                    <th>是否运动</th>
                    <th>体重</th>
                    <th>备注</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr 
                    v-for="(row, index) in filteredHealthData" 
                    :key="index" 
                    class="health-data-row" 
                    :class="{'highlight': index === activeRow}"
                  >
                    <td class="health-date-cell" @click="activeRow = index">
                      <div class="health-date-content">
                        <span class="health-date">{{ row.date }}</span>
                        <span class="health-day">{{ row.day }}</span>
                      </div>
                    </td>
                    <td class="health-meals-cell">
                      <div class="health-meals-container">
                        <div 
                          v-for="meal in row.meals" 
                          :key="meal.type" 
                          class="health-meal-item" 
                          :class="getMealClass(meal.type)"
                        >
                          <div class="health-meal-type">{{ meal.type }}</div>
                          <div class="health-meal-food">{{ meal.food || '未记录' }}</div>
                        </div>
                      </div>
                    </td>
                    <td class="health-exercise-cell">
                      <div v-if="row.exercise" class="health-exercise-badge">
                        <Activity />
                        {{ row.exercise }}
                      </div>
                    </td>
                    <td class="health-weight-cell">
                      <div v-if="row.weight" class="health-weight-badge">
                        <Scale />
                        {{ row.weight }}
                      </div>
                    </td>
                    <td class="health-notes-cell">
                      <div class="health-notes-content">
                        <div class="health-notes-text">{{ row.notes }}</div>
                        <div
                          v-if="row.notes"
                          class="health-notes-expand"
                          @click="expandNotes(index)"
                        >
                          <ChevronDown />
                        </div>
                      </div>
                    </td>
                    <td class="health-actions-cell">
                      <div class="health-actions">
                        <button
                          class="health-action-btn edit-btn"
                          @click="openEditModal(row)"
                          title="编辑"
                        >
                          <Edit />
                        </button>
                        <button
                          class="health-action-btn delete-btn"
                          @click="confirmDeleteRecord(row)"
                          title="删除"
                        >
                          <Trash2 />
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 统计卡片 -->
            <div class="health-stats-container">
              <div class="health-stat-card exercise">
                <div class="health-stat-icon">
                  <Clock />
                </div>
                <div class="health-stat-content">
                  <h3>运动记录</h3>
                  <p>坚持锻炼，保持健康</p>
                  <div class="health-stat-progress">
                    <div class="health-progress-bar" :style="{ width: '65%' }"></div>
                  </div>
                </div>
              </div>

              <div class="health-stat-card diet">
                <div class="health-stat-icon">
                  <Utensils />
                </div>
                <div class="health-stat-content">
                  <h3>饮食管理</h3>
                  <p>合理搭配，营养均衡</p>
                  <div class="health-stat-progress">
                    <div class="health-progress-bar" :style="{ width: '80%' }"></div>
                  </div>
                </div>
              </div>

              <div class="health-stat-card weight">
                <div class="health-stat-icon">
                  <BarChart3 />
                </div>
                <div class="health-stat-content">
                  <h3>体重监控</h3>
                  <p>定期记录，跟踪变化</p>
                  <div class="health-stat-progress">
                    <div class="health-progress-bar" :style="{ width: '45%' }"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>

  <!-- 备注详情模态框 -->
  <Transition name="modal-fade">
    <div v-if="showNotesModal" class="health-modal" @click="closeNotesModal">
      <div class="health-modal-content" @click.stop>
        <div class="health-modal-header">
          <h3>详细备注</h3>
          <button class="health-close-button" @click="closeNotesModal">×</button>
        </div>
        <div class="health-modal-body">
          <p>{{ selectedNote }}</p>
        </div>
      </div>
    </div>
  </Transition>

  <!-- 体重统计模态框 -->
  <Transition name="modal-fade">
    <div v-if="showWeightStats" class="weight-stats-overlay" @click.self="closeWeightStats">
      <Transition name="weight-bounce">
        <div v-if="showWeightStats" class="weight-stats-container">
          <!-- 炫酷背景效果 -->
          <div class="weight-bg-effects">
            <div class="weight-bg-gradient"></div>
            <div class="weight-bg-mesh"></div>
            <div class="weight-particles-advanced">
              <div v-for="i in 30" :key="i" class="weight-particle-advanced" :style="getAdvancedParticleStyle(i)"></div>
            </div>
          </div>

          <!-- 头部 -->
          <div class="weight-stats-header">
            <div class="weight-stats-title-container">
              <h2 class="weight-stats-title">体重变化趋势</h2>
              <div class="weight-stats-title-glow"></div>
            </div>
            <button @click="closeWeightStats" class="weight-stats-close-btn">
              <X class="weight-icon" />
            </button>
          </div>

          <!-- 内容区域 -->
          <div class="weight-stats-content">
            <!-- 统计卡片 -->
            <div class="weight-summary-cards">
              <div class="weight-summary-card current">
                <div class="weight-summary-icon">
                  <Scale />
                </div>
                <div class="weight-summary-info">
                  <h3>当前体重</h3>
                  <p class="weight-value">{{ weightStats.current }}斤</p>
                  <div class="weight-card-glow current-glow"></div>
                </div>
              </div>

              <div class="weight-summary-card loss">
                <div class="weight-summary-icon">
                  <TrendingDown />
                </div>
                <div class="weight-summary-info">
                  <h3>减重成果</h3>
                  <p class="weight-value">-{{ weightStats.loss.toFixed(1) }}斤</p>
                  <div class="weight-card-glow loss-glow"></div>
                </div>
              </div>

              <div class="weight-summary-card percentage">
                <div class="weight-summary-icon">
                  <BarChart3 />
                </div>
                <div class="weight-summary-info">
                  <h3>减重比例</h3>
                  <p class="weight-value">{{ weightStats.lossPercentage }}%</p>
                  <div class="weight-card-glow percentage-glow"></div>
                </div>
              </div>
            </div>

            <!-- ECharts图表容器 -->
            <div class="weight-chart-container">
              <!-- 图表控制栏 -->
              <div class="weight-chart-header">
                <div class="weight-chart-title-section">
                  <h3>体重变化曲线</h3>
                  <div class="weight-range">
                    <span class="weight-range-item">最高: {{ weightStats.max }}斤</span>
                    <span class="weight-range-item">最低: {{ weightStats.min }}斤</span>
                  </div>
                </div>

                <div class="weight-chart-controls">
                  <!-- 日期范围选择 -->
                  <div class="date-range-selector">
                    <Calendar class="selector-icon" />
                    <select v-model="dateRange" class="date-range-select">
                      <option value="all">全部</option>
                      <option value="7days">7天</option>
                      <option value="30days">30天</option>
                      <option value="90days">90天</option>
                    </select>
                  </div>

                  <!-- 图表类型切换 -->
                  <div class="chart-type-selector">
                    <button 
                      v-for="type in chartTypes" 
                      :key="type.value"
                      @click="chartType = type.value"
                      class="chart-type-btn"
                      :class="{ active: chartType === type.value }"
                    >
                      <component :is="type.icon" class="chart-type-icon" />
                      {{ type.label }}
                    </button>
                  </div>
                </div>
              </div>

              <!-- ECharts图表 -->
              <div class="echarts-wrapper">
                <div 
                  ref="chartRef"
                  class="echarts-container"
                  :style="{ height: '400px' }"
                ></div>
                <div class="chart-overlay-effects">
                  <div class="chart-glow-effect"></div>
                </div>
              </div>
            </div>

            <!-- 进度指示器 -->
            <div class="weight-progress-section">
              <div class="progress-header">
                <h4>减重进度</h4>
                <div class="progress-stats">
                  <span class="progress-current">{{ weightStats.lossPercentage }}%</span>
                  <span class="progress-target">目标: 5%</span>
                </div>
              </div>
              <div class="weight-progress-bar-advanced">
                <div class="progress-track"></div>
                <div 
                  class="progress-fill" 
                  :style="{ width: `${Math.min(parseFloat(weightStats.lossPercentage) * 20, 100)}%` }"
                >
                  <div class="progress-shine"></div>
                </div>
                <div class="progress-markers">
                  <div v-for="i in 5" :key="i" class="progress-marker" :style="{ left: `${i * 20}%` }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>

  <!-- 编辑/新增模态框 -->
  <Transition name="modal-fade">
    <div v-if="showEditModal" class="edit-modal-overlay" @click.self="closeEditModal">
      <Transition name="edit-bounce">
        <div v-if="showEditModal" class="edit-modal-container">
          <!-- 头部 -->
          <div class="edit-modal-header">
            <h2 class="edit-modal-title">
              {{ isEditing ? '编辑健康记录' : '新增健康记录' }}
            </h2>
            <button @click="closeEditModal" class="edit-modal-close-btn">
              <X class="edit-icon" />
            </button>
          </div>

          <!-- 表单内容 -->
          <div class="edit-modal-content">
            <form @submit.prevent="saveRecord" class="edit-form">
              <!-- 日期和星期 -->
              <div class="form-row">
                <div class="form-group">
                  <label>日期</label>
                  <input
                    type="text"
                    v-model="editForm.date"
                    placeholder="如: 6.5"
                    class="form-input"
                    required
                  />
                </div>
                <div class="form-group">
                  <label>星期</label>
                  <input
                    type="text"
                    v-model="editForm.day"
                    placeholder="如: 四"
                    class="form-input"
                    required
                  />
                </div>
              </div>

              <!-- 餐食 -->
              <div class="form-group meals-group">
                <label>餐食记录</label>
                <div class="meals-container">
                  <div
                    v-for="(meal, index) in editForm.meals"
                    :key="index"
                    class="meal-input-group"
                  >
                    <span class="meal-label">{{ meal.type }}</span>
                    <input
                      type="text"
                      v-model="meal.food"
                      :placeholder="`请输入${meal.type}内容`"
                      class="form-input meal-input"
                    />
                  </div>
                </div>
              </div>

              <!-- 运动和体重 -->
              <div class="form-row">
                <div class="form-group">
                  <label>运动记录</label>
                  <input
                    type="text"
                    v-model="editForm.exercise"
                    placeholder="运动内容"
                    class="form-input"
                  />
                </div>
                <div class="form-group">
                  <label>体重 (斤)</label>
                  <input
                    type="text"
                    v-model="editForm.weight"
                    placeholder="如: 158.5"
                    class="form-input"
                  />
                </div>
              </div>

              <!-- 备注 -->
              <div class="form-group">
                <label>备注</label>
                <textarea
                  v-model="editForm.notes"
                  placeholder="请输入备注信息..."
                  class="form-textarea"
                  rows="4"
                ></textarea>
              </div>

              <!-- 按钮 -->
              <div class="form-actions">
                <button type="button" @click="closeEditModal" class="btn-cancel">
                  <Cancel />
                  <span>取消</span>
                </button>
                <button type="submit" class="btn-save">
                  <Save />
                  <span>{{ isEditing ? '更新' : '保存' }}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted, watch, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { healthDBOperations } from '../lib/healthDB.js'
import {
  X,
  Search,
  TrendingDown,
  Activity,
  Scale,
  ChevronDown,
  Clock,
  Utensils,
  BarChart3,
  Calendar,
  LineChart,
  AreaChart,
  Zap,
  Plus,
  Edit,
  Trash2,
  Save,
  Cancel
} from 'lucide-vue-next'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'data-updated'])

// 响应式数据
const searchQuery = ref('')
const activeRow = ref(null)
const showNotesModal = ref(false)
const selectedNote = ref('')
const showWeightStats = ref(false)

// 编辑功能相关状态
const showEditModal = ref(false)
const editingRecord = ref(null)
const editForm = ref({
  date: '',
  day: '',
  meals: [
    { type: '早餐', food: '' },
    { type: '午饭', food: '' },
    { type: '晚上', food: '' }
  ],
  exercise: '',
  weight: '',
  notes: ''
})
const isEditing = ref(false) // 是否为编辑模式（true为编辑，false为新增）

// 图表相关状态
const dateRange = ref('all')
const chartType = ref('line')
const chartRef = ref(null)
let chartInstance = null

// 图表类型选项
const chartTypes = [
  { value: 'line', label: '线性图', icon: LineChart },
  { value: 'area', label: '面积图', icon: AreaChart },
  { value: 'smooth', label: '平滑曲线', icon: Zap }
]

// 健康数据
const healthData = ref([])

// 默认健康数据（用于初始化数据库）
const defaultHealthData = [
  {
    date: '6.5',
    day: '四',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '158.5',
    notes: '6:20：起床喝温水，6分钟拉伸。6:35：洗漱。7:00：出门送老婆。7:40：快走30分钟。11:50：午餐，饭后散步20-40分钟。18:30：晚餐，饭后站立30分钟。20:00：运动（按计划）。23:00前：入睡。'
  },
  {
    date: '6.6',
    day: '五',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '158.5',
    notes: '每天额外步行≥6000步（如通勤、散步）运动前后补充水分，避免空腹运动（防低血糖）。'
  },
  {
    date: '6.7',
    day: '六',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '157.0',
    notes: '每天早晨起床喝200-300ml温水40-50℃，刷牙后、早餐前饮用柠檬片1片（维生素C促进代谢，胃酸过多者慎用）。生姜2薄片（驱寒暖胃，适合体寒者）。喝完温水后做5分钟拉伸（如猫式伸展），进一步激活代谢'
  },
  {
    date: '6.8',
    day: '日',
    meals: [
      { type: '早餐', food: '粥' },
      { type: '午饭', food: '凉拌面' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '156.4',
    notes: '戒除：含糖饮料（包括果汁）、酒精、油炸食品、甜点、白面包/白米饭（替换为粗粮）'
  },
  {
    date: '6.9',
    day: '一',
    meals: [
      { type: '早餐', food: '面包片1、牛奶、' },
      { type: '午饭', food: '米饭、黄瓜猪头肉、角瓜木耳' },
      { type: '晚上', food: '水煮虾、鸡蛋柿子、小黄饼' }
    ],
    exercise: '',
    weight: '155.9',
    notes: '会议，适当适宜以来大家'
  },
  {
    date: '6.10',
    day: '二',
    meals: [
      { type: '早餐', food: '地瓜1、鸡蛋1' },
      { type: '午饭', food: '米饭、玉米排骨豆角、黄瓜沾酱' },
      { type: '晚上', food: '满宝馄饨8个' }
    ],
    exercise: '',
    weight: '155.8',
    notes: ''
  },
  {
    date: '6.11',
    day: '三',
    meals: [
      { type: '早餐', food: '香蕉1、鸡蛋1' },
      { type: '午饭', food: '米饭、水煮肉片' },
      { type: '晚上', food: '麻辣烫' }
    ],
    exercise: '',
    weight: '155.0',
    notes: ''
  },
  {
    date: '6.12',
    day: '四',
    meals: [
      { type: '早餐', food: '全麦吐司1、脱脂牛奶1、鸡蛋1' },
      { type: '午饭', food: '红枣发糕、家常凉菜、鳕鱼、鸡蛋柿子' },
      { type: '晚上', food: '馄饨10' }
    ],
    exercise: '',
    weight: '154.3',
    notes: ''
  },
  {
    date: '6.13',
    day: '五',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '153.7',
    notes: ''
  },
  {
    date: '6.14',
    day: '六',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '153.7',
    notes: ''
  },
  {
    date: '6.15',
    day: '日',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '153.2',
    notes: ''
  },
  {
    date: '6.16',
    day: '一',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '152.8',
    notes: ''
  },
  {
    date: '6.17',
    day: '二',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '152.7',
    notes: ''
  },
  {
    date: '6.18',
    day: '三',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '151.2',
    notes: ''
  },
  {
    date: '6.19',
    day: '四',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '150.9',
    notes: ''
  },
  {
    date: '6.20',
    day: '五',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '151.1',
    notes: ''
  },
  {
    date: '6.21',
    day: '六',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '150.0',
    notes: ''
  },
  {
    date: '6.22',
    day: '日',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '150.6',
    notes: ''
  },
  {
    date: '6.23',
    day: '一',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '150.4',
    notes: ''
  },
  {
    date: '6.24',
    day: '二',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '148.7',
    notes: ''
  },
  {
    date: '6.25',
    day: '三',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '148.0',
    notes: ''
  },
  {
    date: '6.26',
    day: '四',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '146.8',
    notes: ''
  },
  {
    date: '6.27',
    day: '五',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '146.0',
    notes: ''
  },
  {
    date: '6.28',
    day: '六',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '145.2',
    notes: ''
  },
  {
    date: '6.29',
    day: '日',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '146',
    notes: ''
  },
  {
    date: '6.30',
    day: '一',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '147.5',
    notes: ''
  },
  {
    date: '7.1',
    day: '二',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '147.3',
    notes: ''
  },
  {
    date: '7.2',
    day: '三',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '146.4',
    notes: ''
  },
  {
    date: '7.8',
    day: '一',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '144.6',
    notes: ''
  },
  {
    date: '7.14',
    day: '一',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '146.3',
    notes: ''
  },
  {
    date: '7.15',
    day: '二',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '145.0',
    notes: ''
  },
  {
    date: '7.16',
    day: '三',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '143.4',
    notes: ''
  },
  {
    date: '7.17',
    day: '四',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '143.6',
    notes: ''
  }
]

// 计算属性
const filteredHealthData = computed(() => {
  if (!searchQuery.value) return healthData.value
  return healthData.value.filter(row =>
    row.date.includes(searchQuery.value) ||
    row.notes.includes(searchQuery.value) ||
    row.meals.some(meal => meal.food.includes(searchQuery.value))
  )
})

// 根据日期范围过滤体重数据
const filteredWeightData = computed(() => {
  const validData = healthData.value.filter(d => d.weight && !isNaN(parseFloat(d.weight)))

  if (dateRange.value === 'all') return validData

  const now = new Date()
  const filterDate = new Date()

  switch (dateRange.value) {
    case '7days':
      filterDate.setDate(now.getDate() - 7)
      break
    case '30days':
      filterDate.setDate(now.getDate() - 30)
      break
    case '90days':
      filterDate.setDate(now.getDate() - 90)
      break
    default:
      return validData
  }

  return validData.filter(d => {
    const [month, day] = d.date.split('.')
    const itemDate = new Date(2024, parseInt(month) - 1, parseInt(day))
    return itemDate >= filterDate
  })
})

// 计算体重统计
const weightStats = computed(() => {
  const weights = filteredWeightData.value.map(d => parseFloat(d.weight)).filter(w => !isNaN(w))
  if (weights.length === 0) return { current: 0, start: 0, min: 0, max: 0, loss: 0, lossPercentage: '0.0' }

  const minWeight = Math.min(...weights)
  const maxWeight = Math.max(...weights)
  const currentWeight = weights[weights.length - 1]
  const startWeight = weights[0]
  const weightLoss = startWeight - currentWeight

  return {
    current: currentWeight,
    start: startWeight,
    min: minWeight,
    max: maxWeight,
    loss: weightLoss,
    lossPercentage: ((weightLoss / startWeight) * 100).toFixed(1)
  }
})

// ECharts配置
const getChartOption = () => {
  const dates = filteredWeightData.value.map(d => d.date)
  const weights = filteredWeightData.value.map(d => parseFloat(d.weight))

  if (weights.length === 0) return {}

  // 计算体重范围，添加小幅度的padding让图表更美观
  const minWeight = Math.min(...weights)
  const maxWeight = Math.max(...weights)
  const weightRange = maxWeight - minWeight
  const padding = Math.max(weightRange * 0.05, 0.5) // 5%的padding，最小0.5斤

  const yAxisMin = Math.max(0, minWeight - padding)
  const yAxisMax = maxWeight + padding

  const baseOption = {
    backgroundColor: 'transparent',
    animation: true,
    animationDuration: 2000,
    animationEasing: 'cubicOut',
    grid: {
      left: '5%',
      right: '5%',
      top: '15%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 2
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'bold'
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: yAxisMin,
      max: yAxisMax,
      interval: Math.max((yAxisMax - yAxisMin) / 5, 0.5), // 自动计算合适的间隔
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
          width: 2
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.8)',
        fontSize: 12,
        fontWeight: 'bold',
        formatter: '{value}斤'
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      borderColor: '#ff6b6b',
      borderWidth: 2,
      textStyle: {
        color: '#fff',
        fontSize: 14,
        fontWeight: 'bold'
      },
      formatter: function(params) {
        const param = params[0]
        const currentWeight = param.value
        const previousWeight = weights[param.dataIndex - 1]
        let changeText = ''
        
        if (previousWeight !== undefined) {
          const change = currentWeight - previousWeight
          const changeIcon = change > 0 ? '📈' : change < 0 ? '📉' : '➡️'
          const changeColor = change > 0 ? '#ff6b6b' : change < 0 ? '#4caf50' : '#ffc107'
          changeText = `
            <div style="color: ${changeColor}; font-size: 14px; margin-top: 5px;">
              ${changeIcon} ${change > 0 ? '+' : ''}${change.toFixed(1)}斤
            </div>
          `
        }
        
        return `
          <div style="padding: 10px;">
            <div style="color: #ff6b6b; font-size: 16px; margin-bottom: 8px;">
              📅 ${param.axisValue}
            </div>
            <div style="color: #fff; font-size: 18px; font-weight: bold;">
              ⚖️ ${param.value}斤
            </div>
            ${changeText}
          </div>
        `
      },
      extraCssText: 'border-radius: 12px; box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);'
    }
  }

  // 根据图表类型设置不同的系列配置
  if (chartType.value === 'line') {
    baseOption.series = [{
      name: '体重',
      type: 'line',
      data: weights,
      smooth: false,
      symbol: 'circle',
      symbolSize: 10,
      lineStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#ff6b6b' },
          { offset: 0.5, color: '#ee5a52' },
          { offset: 1, color: '#ff9a9e' }
        ]),
        width: 4,
        shadowColor: 'rgba(255, 107, 107, 0.5)',
        shadowBlur: 10,
        shadowOffsetY: 2
      },
      itemStyle: {
        color: '#ff6b6b',
        borderColor: '#fff',
        borderWidth: 3,
        shadowColor: 'rgba(255, 107, 107, 0.8)',
        shadowBlur: 15
      },
      emphasis: {
        itemStyle: {
          color: '#ff6b6b',
          borderColor: '#fff',
          borderWidth: 4,
          shadowColor: 'rgba(255, 107, 107, 1)',
          shadowBlur: 20,
          scale: 1.8
        }
      },
      markLine: {
        silent: true,
        lineStyle: {
          color: 'rgba(255, 193, 7, 0.6)',
          type: 'dashed',
          width: 2
        },
        data: [
          { yAxis: minWeight, name: '最低体重' },
          { yAxis: maxWeight, name: '最高体重' }
        ],
        label: {
          show: true,
          position: 'end',
          formatter: '{b}: {c}斤',
          color: 'rgba(255, 193, 7, 0.9)',
          fontSize: 12,
          fontWeight: 'bold'
        }
      }
    }]
  } else if (chartType.value === 'area') {
    baseOption.series = [{
      name: '体重',
      type: 'line',
      data: weights,
      smooth: false,
      symbol: 'circle',
      symbolSize: 10,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 107, 107, 0.8)' },
          { offset: 0.5, color: 'rgba(238, 90, 82, 0.4)' },
          { offset: 1, color: 'rgba(255, 154, 158, 0.1)' }
        ]),
        shadowColor: 'rgba(255, 107, 107, 0.3)',
        shadowBlur: 20
      },
      lineStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#ff6b6b' },
          { offset: 0.5, color: '#ee5a52' },
          { offset: 1, color: '#ff9a9e' }
        ]),
        width: 4,
        shadowColor: 'rgba(255, 107, 107, 0.5)',
        shadowBlur: 10
      },
      itemStyle: {
        color: '#ff6b6b',
        borderColor: '#fff',
        borderWidth: 3,
        shadowColor: 'rgba(255, 107, 107, 0.8)',
        shadowBlur: 15
      },
      emphasis: {
        itemStyle: {
          scale: 1.8,
          shadowBlur: 25
        }
      }
    }]
  } else if (chartType.value === 'smooth') {
    baseOption.series = [{
      name: '体重',
      type: 'line',
      data: weights,
      smooth: true,
      smoothMonotone: 'x',
      symbol: 'circle',
      symbolSize: 12,
      lineStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color: '#ff6b6b' },
          { offset: 0.3, color: '#ee5a52' },
          { offset: 0.6, color: '#ff9a9e' },
          { offset: 1, color: '#fad0c4' }
        ]),
        width: 5,
        shadowColor: 'rgba(255, 107, 107, 0.6)',
        shadowBlur: 15,
        shadowOffsetY: 3
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(255, 107, 107, 0.6)' },
          { offset: 0.5, color: 'rgba(238, 90, 82, 0.3)' },
          { offset: 1, color: 'rgba(255, 154, 158, 0.05)' }
        ])
      },
      itemStyle: {
        color: new echarts.graphic.RadialGradient(0.5, 0.5, 0.5, [
          { offset: 0, color: '#fff' },
          { offset: 0.7, color: '#ff6b6b' },
          { offset: 1, color: '#ee5a52' }
        ]),
        borderColor: '#fff',
        borderWidth: 4,
        shadowColor: 'rgba(255, 107, 107, 1)',
        shadowBlur: 20
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 30,
          scale: 2
        }
      }
    }]
  }

  return baseOption
}

// 初始化图表
const initChart = () => {
  if (chartRef.value && !chartInstance) {
    chartInstance = echarts.init(chartRef.value, null, {
      renderer: 'canvas',
      useDirtyRect: false
    })
    
    // 添加图表事件
    chartInstance.on('click', (params) => {
      console.log('点击了数据点:', params)
    })
    
    updateChart()
  }
}

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = getChartOption()
    chartInstance.setOption(option, true)
  }
}

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
}

// 监听数据变化
watch([filteredWeightData, chartType], () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 监听窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 数据库操作方法
const loadHealthData = async () => {
  try {
    const records = await healthDBOperations.getAllHealthRecords()
    if (records.length === 0) {
      // 如果数据库为空，导入默认数据
      await healthDBOperations.bulkImportHealthRecords(defaultHealthData)
      healthData.value = [...defaultHealthData]
    } else {
      healthData.value = records
    }
  } catch (error) {
    console.error('加载健康数据失败:', error)
    // 如果数据库操作失败，使用默认数据
    healthData.value = [...defaultHealthData]
  }
}

const saveHealthRecord = async (record) => {
  try {
    if (record.id) {
      // 更新现有记录
      await healthDBOperations.updateHealthRecord(record.id, record)
    } else {
      // 添加新记录
      await healthDBOperations.addHealthRecord(record)
    }
    await loadHealthData() // 重新加载数据
    emit('data-updated') // 通知父组件数据已更新
  } catch (error) {
    console.error('保存健康记录失败:', error)
    throw error
  }
}

const deleteHealthRecord = async (id) => {
  try {
    await healthDBOperations.deleteHealthRecord(id)
    await loadHealthData() // 重新加载数据
    emit('data-updated') // 通知父组件数据已更新
  } catch (error) {
    console.error('删除健康记录失败:', error)
    throw error
  }
}

// 生命周期
onMounted(async () => {
  window.addEventListener('resize', handleResize)
  await loadHealthData() // 加载健康数据
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  destroyChart()
})

// 监听体重统计模态框显示状态
watch(showWeightStats, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initChart()
    })
  } else {
    destroyChart()
  }
})

// 方法
const getMealClass = (type) => {
  switch (type) {
    case '早餐':
      return 'breakfast'
    case '午饭':
      return 'lunch'
    case '晚上':
      return 'dinner'
    default:
      return ''
  }
}

const expandNotes = (index) => {
  selectedNote.value = filteredHealthData.value[index].notes
  showNotesModal.value = true
}

const closeNotesModal = () => {
  showNotesModal.value = false
}

const openWeightStats = () => {
  showWeightStats.value = true
}

const closeWeightStats = () => {
  showWeightStats.value = false
}

// 编辑功能方法
const openAddModal = () => {
  isEditing.value = false
  editingRecord.value = null
  editForm.value = {
    date: '',
    day: '',
    meals: [
      { type: '早餐', food: '' },
      { type: '午饭', food: '' },
      { type: '晚上', food: '' }
    ],
    exercise: '',
    weight: '',
    notes: ''
  }
  showEditModal.value = true
}

const openEditModal = (record) => {
  isEditing.value = true
  editingRecord.value = record
  editForm.value = {
    date: record.date,
    day: record.day,
    meals: [...record.meals],
    exercise: record.exercise,
    weight: record.weight,
    notes: record.notes
  }
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  editingRecord.value = null
  isEditing.value = false
}

const saveRecord = async () => {
  try {
    if (!editForm.value.date || !editForm.value.day) {
      alert('请填写日期和星期')
      return
    }

    const recordToSave = {
      ...editForm.value,
      id: editingRecord.value?.id
    }

    await saveHealthRecord(recordToSave)
    closeEditModal()
    console.log('记录保存成功')
  } catch (error) {
    console.error('保存记录失败:', error)
    alert('保存失败，请重试')
  }
}

const confirmDeleteRecord = async (record) => {
  if (confirm(`确定要删除 ${record.date} 的记录吗？`)) {
    try {
      await deleteHealthRecord(record.id)
      console.log('记录删除成功')
    } catch (error) {
      console.error('删除记录失败:', error)
      alert('删除失败，请重试')
    }
  }
}

// 样式计算方法
const getParticleStyle = (index) => {
  return {
    left: `${Math.random() * 100}%`,
    animationDelay: `${index * 0.5}s`,
    animationDuration: `${3 + Math.random() * 2}s`
  }
}

const getAdvancedParticleStyle = (index) => {
  return {
    left: `${Math.random() * 100}%`,
    top: `${Math.random() * 100}%`,
    animationDelay: `${index * 0.2}s`,
    animationDuration: `${4 + Math.random() * 3}s`,
    '--particle-color': `hsl(${Math.random() * 60 + 340}, 70%, 60%)`
  }
}
</script>

<style scoped>

button:focus {
  outline: none;
}
/* 基础样式 */
.health-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-container {
  width: 95%;
  max-width: 1400px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* 粒子效果 */
.health-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.health-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 107, 107, 0.6);
  border-radius: 50%;
  animation: healthFloat 12s infinite linear;
  opacity: 0;
}

@keyframes healthFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) scale(1);
    opacity: 0;
  }
}

/* 头部样式 */
.health-header {
  text-align: center;
  padding: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(20, 20, 35, 0.8);
  position: relative;
  z-index: 10;
}

.health-title-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.health-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52, #ff9a9e, #fad0c4);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  letter-spacing: 2px;
  margin: 0;
  position: relative;
  z-index: 2;
}

.health-title-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52, #ff9a9e, #fad0c4);
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
  filter: blur(20px);
  opacity: 0.3;
  z-index: 1;
}

.health-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
}

.health-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 40px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.health-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.health-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* 内容区域 */
.health-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
}

/* 控制栏 */
.health-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(30, 30, 46, 0.8);
}

.health-search-box {
  position: relative;
  width: 300px;
}

.health-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.health-search-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 107, 107, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.health-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.health-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  width: 20px;
  height: 20px;
}

.health-filter-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 50px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.health-filter-button:hover {
  background: rgba(255, 107, 107, 0.3);
}

/* 表格样式 */
.health-table-wrapper {
  flex: 1;
  overflow: auto;
  padding: 20px;
}

.health-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

.health-table thead th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  background: rgba(48, 43, 99, 0.9);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.health-data-row {
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  border-radius: 12px;
}

.health-data-row td {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.health-data-row td:first-child {
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  border-left: 1px solid rgba(255, 255, 255, 0.05);
}

.health-data-row td:last-child {
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.health-data-row:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.health-data-row.highlight {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* 日期单元格 */
.health-date-cell {
  width: 100px;
  cursor: pointer;
}

.health-date-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.health-date-cell:hover .health-date-content {
  background: rgba(255, 107, 107, 0.2);
  transform: scale(1.05);
}

.health-date {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
}

.health-day {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
}

/* 餐食单元格 */
.health-meals-cell {
  width: 35%;
}

.health-meals-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.health-meal-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.health-meal-item:hover {
  transform: translateX(5px);
}

.health-meal-item.breakfast {
  background: rgba(255, 193, 7, 0.1);
  border-left: 3px solid rgba(255, 193, 7, 0.5);
}

.health-meal-item.lunch {
  background: rgba(255, 87, 34, 0.1);
  border-left: 3px solid rgba(255, 87, 34, 0.5);
}

.health-meal-item.dinner {
  background: rgba(103, 58, 183, 0.1);
  border-left: 3px solid rgba(103, 58, 183, 0.5);
}

.health-meal-type {
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  margin-right: 0.75rem;
  min-width: 60px;
  text-align: center;
}

.breakfast .health-meal-type {
  background: rgba(255, 193, 7, 0.2);
  color: rgba(255, 193, 7, 0.9);
}

.lunch .health-meal-type {
  background: rgba(255, 87, 34, 0.2);
  color: rgba(255, 87, 34, 0.9);
}

.dinner .health-meal-type {
  background: rgba(103, 58, 183, 0.2);
  color: rgba(103, 58, 183, 0.9);
}

.health-meal-food {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  flex: 1;
}

/* 运动单元格 */
.health-exercise-cell {
  width: 100px;
  text-align: center;
}

.health-exercise-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 50px;
  color: rgba(76, 175, 80, 0.9);
  font-weight: 600;
  transition: all 0.3s ease;
}

.health-exercise-badge:hover {
  background: rgba(76, 175, 80, 0.3);
  transform: scale(1.05);
}

/* 体重单元格 */
.health-weight-cell {
  width: 100px;
  text-align: center;
}

.health-weight-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.2), rgba(156, 39, 176, 0.2));
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 50px;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.health-weight-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.3);
}

/* 备注单元格 */
.health-notes-cell {
  width: 25%;
}

.health-notes-content {
  position: relative;
  max-height: 80px;
  overflow: hidden;
  padding-right: 20px;
}

.health-notes-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.health-notes-expand {
  position: absolute;
  bottom: 0;
  right: 0;
  background: linear-gradient(to left, rgba(48, 43, 99, 0.9) 0%, rgba(48, 43, 99, 0) 100%);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.health-notes-expand:hover {
  transform: translateY(-2px);
}

/* 统计卡片 */
.health-stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 20px;
  background: rgba(20, 20, 35, 0.5);
}

.health-stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.health-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.health-stat-card:hover {
  transform: translateY(-5px);
}

.health-stat-card:hover::before {
  opacity: 1;
}

.health-stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.exercise .health-stat-icon {
  background: rgba(33, 150, 243, 0.2);
  color: rgba(33, 150, 243, 0.9);
}

.diet .health-stat-icon {
  background: rgba(76, 175, 80, 0.2);
  color: rgba(76, 175, 80, 0.9);
}

.weight .health-stat-icon {
  background: rgba(156, 39, 176, 0.2);
  color: rgba(156, 39, 176, 0.9);
}

.health-stat-card:hover .health-stat-icon {
  transform: scale(1.1) rotate(5deg);
}

.health-stat-content {
  flex: 1;
}

.health-stat-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: white;
}

.health-stat-content p {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
}

.health-stat-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.health-progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 1s ease;
}

.exercise .health-progress-bar {
  background: linear-gradient(to right, #2196f3, #03a9f4);
}

.diet .health-progress-bar {
  background: linear-gradient(to right, #4caf50, #8bc34a);
}

.weight .health-progress-bar {
  background: linear-gradient(to right, #9c27b0, #673ab7);
}

/* 备注模态框 */
.health-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200;
}

.health-modal-content {
  background: linear-gradient(135deg, #302b63, #24243e);
  border-radius: 16px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.health-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.health-modal-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  background: linear-gradient(90deg, #ff6b6b, #ee5a52);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.health-close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.health-close-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

.health-modal-body {
  padding: 1.5rem;
}

.health-modal-body p {
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* 体重统计模态框 */
.weight-stats-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 150;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weight-stats-container {
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* 炫酷背景效果 */
.weight-bg-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.weight-bg-gradient {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at 30% 20%, rgba(255, 107, 107, 0.15) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(238, 90, 82, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 20% 70%, rgba(255, 154, 158, 0.08) 0%, transparent 50%);
  animation: bgRotate 20s linear infinite;
}

.weight-bg-mesh {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 107, 107, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 107, 107, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: meshMove 15s linear infinite;
}

.weight-particles-advanced {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.weight-particle-advanced {
  position: absolute;
  width: 3px;
  height: 3px;
  background: var(--particle-color, #ff6b6b);
  border-radius: 50%;
  animation: advancedFloat 20s infinite linear;
  opacity: 0;
  box-shadow: 0 0 6px var(--particle-color, #ff6b6b);
}

@keyframes bgRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes meshMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes advancedFloat {
  0% {
    transform: translateY(100vh) translateX(0) scale(0) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 1;
  }
  95% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(200px) scale(1.5) rotate(720deg);
    opacity: 0;
  }
}

.weight-stats-header {
  text-align: center;
  padding: 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(20, 20, 35, 0.8);
  position: relative;
  z-index: 10;
}

.weight-stats-title-container {
  position: relative;
  display: inline-block;
}

.weight-stats-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52, #ff9a9e, #fad0c4);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  letter-spacing: 2px;
  margin: 0;
  position: relative;
  z-index: 2;
}

.weight-stats-title-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52, #ff9a9e, #fad0c4);
  background-size: 300% 300%;
  animation: gradient 8s ease infinite;
  filter: blur(30px);
  opacity: 0.4;
  z-index: 1;
}

.weight-stats-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 12px;
  height: 40px;
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.weight-stats-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.weight-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.weight-stats-content {
  flex: 1;
  overflow-y: auto;
  padding: 30px;
  position: relative;
  z-index: 10;
}

/* 统计卡片 */
.weight-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.weight-summary-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.weight-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.weight-summary-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.weight-summary-card:hover::before {
  opacity: 1;
}

.weight-summary-icon {
  width: 70px;
  height: 70px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  position: relative;
}

.current .weight-summary-icon {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.3), rgba(33, 150, 243, 0.1));
  color: #2196f3;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.3);
}

.loss .weight-summary-icon {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  color: #4caf50;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.percentage .weight-summary-icon {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  color: #9c27b0;
  box-shadow: 0 0 20px rgba(156, 39, 176, 0.3);
}

.weight-summary-card:hover .weight-summary-icon {
  transform: scale(1.15) rotate(10deg);
}

.weight-summary-info {
  flex: 1;
  position: relative;
}

.weight-summary-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.5px;
}

.weight-value {
  font-size: 2.2rem;
  font-weight: 800;
  margin: 0;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52, #ff9a9e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.weight-card-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  opacity: 0.1;
  filter: blur(20px);
  transition: opacity 0.3s ease;
}

.current-glow {
  background: radial-gradient(circle, #2196f3, transparent);
}

.loss-glow {
  background: radial-gradient(circle, #4caf50, transparent);
}

.percentage-glow {
  background: radial-gradient(circle, #9c27b0, transparent);
}

.weight-summary-card:hover .weight-card-glow {
  opacity: 0.3;
}

/* 图表容器 */
.weight-chart-container {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 24px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.weight-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.02) 0%, rgba(238, 90, 82, 0.01) 100%);
  pointer-events: none;
}

.weight-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25px;
  gap: 20px;
  position: relative;
  z-index: 2;
}

.weight-chart-title-section h3 {
  font-size: 1.6rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 10px 0;
  letter-spacing: 1px;
}

.weight-range {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.weight-range-item {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.08);
  padding: 8px 16px;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.weight-range-item:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-2px);
}

.weight-chart-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

/* 日期范围选择器 */
.date-range-selector {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 10px 15px;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.date-range-selector:hover {
  background: rgba(255, 255, 255, 0.15);
}

.selector-icon {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 0.8);
}

.date-range-select {
  background: transparent;
  border: none;
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  outline: none;
  padding: 2px 8px;
}

.date-range-select option {
  background: #302b63;
  color: white;
  padding: 8px;
}

/* 图表类型选择器 */
.chart-type-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-type-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.chart-type-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.chart-type-btn.active {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.3), rgba(238, 90, 82, 0.2));
  color: #ff6b6b;
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.chart-type-icon {
  width: 16px;
  height: 16px;
}

/* ECharts容器 */
.echarts-wrapper {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.echarts-container {
  width: 100%;
  position: relative;
  z-index: 2;
}

.chart-overlay-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.chart-glow-effect {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 60%;
  transform: translate(-50%, -50%);
  background: radial-gradient(ellipse at center, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
  animation: chartGlow 4s ease-in-out infinite alternate;
}

@keyframes chartGlow {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 进度指示器 */
.weight-progress-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.weight-progress-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.02) 0%, rgba(238, 90, 82, 0.01) 100%);
  pointer-events: none;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}

.progress-header h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 0.5px;
}

.progress-stats {
  display: flex;
  gap: 15px;
  align-items: center;
}

.progress-current {
  font-size: 1.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.progress-target {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 12px;
  border-radius: 15px;
}

.weight-progress-bar-advanced {
  position: relative;
  height: 16px;
  margin-bottom: 15px;
  z-index: 2;
}

.progress-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ee5a52, #ff9a9e, #fad0c4);
  border-radius: 8px;
  transition: width 3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.progress-shine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progressShine 2s infinite;
}

.progress-markers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.progress-marker {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 动画 */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 过渡动画 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.health-bounce-enter-active {
  animation: health-bounce-in 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.health-bounce-leave-active {
  animation: health-bounce-in 0.4s cubic-bezier(0.6, -0.28, 0.735, 0.045) reverse;
}

.weight-bounce-enter-active {
  animation: weight-bounce-in 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.weight-bounce-leave-active {
  animation: weight-bounce-in 0.5s cubic-bezier(0.6, -0.28, 0.735, 0.045) reverse;
}

@keyframes health-bounce-in {
  0% {
    transform: scale(0.8) rotateY(-15deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.05) rotateY(5deg);
  }
  100% {
    transform: scale(1) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes weight-bounce-in {
  0% {
    transform: scale(0.7) rotateX(-20deg) rotateY(-15deg);
    opacity: 0;
  }
  30% {
    transform: scale(1.1) rotateX(10deg) rotateY(5deg);
  }
  70% {
    transform: scale(0.95) rotateX(-5deg) rotateY(-2deg);
  }
  100% {
    transform: scale(1) rotateX(0deg) rotateY(0deg);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .weight-chart-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .weight-chart-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-type-selector {
    flex-wrap: wrap;
  }
}

@media (max-width: 992px) {
  .health-controls {
    flex-direction: column;
    gap: 1rem;
  }

  .health-search-box {
    width: 100%;
  }

  .health-stats-container {
    grid-template-columns: 1fr;
  }

  .weight-summary-cards {
    grid-template-columns: 1fr;
  }

  .weight-chart-container {
    padding: 20px;
  }

  .echarts-container {
    height: 300px !important;
  }
}

@media (max-width: 768px) {
  .health-container,
  .weight-stats-container {
    width: 95%;
    height: 90vh;
  }

  .health-title,
  .weight-stats-title {
    font-size: 1.8rem;
  }

  .health-table-wrapper {
    padding: 10px;
  }

  .health-data-row td {
    padding: 0.5rem;
  }

  .weight-stats-content {
    padding: 20px;
  }

  .weight-chart-container {
    padding: 15px;
  }

  .weight-range {
    flex-direction: column;
    gap: 8px;
  }

  .chart-type-selector {
    flex-direction: column;
    gap: 4px;
  }

  .chart-type-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .health-title,
  .weight-stats-title {
    font-size: 1.5rem;
  }

  .health-table {
    font-size: 0.8rem;
  }

  .echarts-container {
    height: 250px !important;
  }

  .weight-summary-card {
    padding: 20px;
    flex-direction: column;
    text-align: center;
  }

  .weight-summary-icon {
    width: 60px;
    height: 60px;
  }

  .weight-value {
    font-size: 1.8rem;
  }
}

/* 控制按钮组 */
.health-control-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.health-add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 50px;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.health-add-button:hover {
  background: rgba(76, 175, 80, 0.3);
  transform: translateY(-2px);
}

/* 操作按钮列 */
.health-actions-cell {
  width: 100px;
  text-align: center;
}

.health-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.health-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.health-action-btn svg {
  width: 16px;
  height: 16px;
}

.edit-btn {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.edit-btn:hover {
  background: rgba(33, 150, 243, 0.3);
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.delete-btn:hover {
  background: rgba(244, 67, 54, 0.3);
  transform: scale(1.1);
}

/* 编辑模态框 */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-modal-container {
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  background: linear-gradient(135deg, #0f0c29, #302b63, #24243e);
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(20, 20, 35, 0.8);
}

.edit-modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 0;
}

.edit-modal-close-btn {
  background: rgba(255, 107, 107, 0.2);
  border: none;
  border-radius: 8px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-modal-close-btn:hover {
  background: rgba(255, 107, 107, 0.4);
}

.edit-icon {
  width: 18px;
  height: 18px;
  color: white;
}

.edit-modal-content {
  padding: 30px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

/* 表单样式 */
.edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.form-input,
.form-textarea {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 107, 107, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* 餐食输入组 */
.meals-group {
  grid-column: 1 / -1;
}

.meals-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.meal-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.meal-label {
  min-width: 60px;
  font-size: 0.85rem;
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 20px;
  text-align: center;
  background: rgba(255, 107, 107, 0.2);
  color: rgba(255, 107, 107, 0.9);
}

.meal-input {
  flex: 1;
}

/* 表单按钮 */
.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-save {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: rgba(158, 158, 158, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.btn-cancel:hover {
  background: rgba(158, 158, 158, 0.3);
}

.btn-save {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.btn-save:hover {
  background: rgba(76, 175, 80, 0.3);
}

.btn-cancel svg,
.btn-save svg {
  width: 16px;
  height: 16px;
}

/* 编辑模态框动画 */
.edit-bounce-enter-active {
  animation: editBounceIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.edit-bounce-leave-active {
  animation: editBounceOut 0.3s ease-in;
}

@keyframes editBounceIn {
  0% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes editBounceOut {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.6), rgba(238, 90, 82, 0.4));
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.8), rgba(238, 90, 82, 0.6));
}
</style>
