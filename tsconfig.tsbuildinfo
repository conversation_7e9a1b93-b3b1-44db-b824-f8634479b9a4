{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/lib/utils.ts", "./src/app.vue", "./src/admin/afoodmall.vue", "./src/admin/amemo.vue", "./src/admin/main.vue", "./src/admin/com.vue", "./src/admin/components/foodmanagement.vue", "./src/admin/components/memomanagement.vue", "./src/admin/components/memosettings.vue", "./src/admin/components/memostatistics.vue", "./src/admin/components/navigatorfour.vue", "./src/admin/components/navigatorthree.vue", "./src/admin/components/navigatortwo.vue", "./src/components/animategrid.vue", "./src/components/bookmarkimporter.vue", "./src/components/codecenter.vue", "./src/components/com.vue", "./src/components/foldertree.vue", "./src/components/foodmall.vue", "./src/components/healthmodal.vue", "./src/components/helloworld - 副本.vue", "./src/components/helloworld.vue", "./src/components/memomodal.vue", "./src/components/passwordvault.vue", "./src/components/search.vue", "./src/components/todolist.vue", "./src/components/videotext.vue", "./src/components/bwl.vue", "./src/components/note.vue", "./auto-imports.d.ts"], "version": "5.8.3"}